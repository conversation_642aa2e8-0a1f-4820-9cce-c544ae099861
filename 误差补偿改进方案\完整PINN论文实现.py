import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from tqdm import tqdm
import warnings
import random
import os
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def set_random_seeds(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)

class EnhancedPhysicsFeatureEngineering:
    def __init__(self):
        self.dh_params = np.array([
            [0, 0, 0.320, 0],
            [-np.pi/2, 0.225, 0, 0],
            [0, 0.225, 0, np.pi/2],
            [-np.pi/2, 0, 0.215, 0],
            [np.pi/2, 0, 0, 0],
            [-np.pi/2, 0, 0.065, 0]
        ])

        self.joint_limits = np.array([
            [-180, 180], [-125, 125], [-138, 138],
            [-270, 270], [-120, 133], [-270, 270]
        ])

        self.gravity_vector = np.array([0, 0, -9.81])

    def create_physics_features(self, joint_angles):
        angles_rad = np.deg2rad(joint_angles)
        features = []

        normalized_angles = []
        for i, angle in enumerate(joint_angles):
            min_limit, max_limit = self.joint_limits[i]
            normalized = 2 * (angle - min_limit) / (max_limit - min_limit) - 1
            normalized_angles.append(normalized)
        features.extend(normalized_angles)

        for angle in angles_rad:
            features.extend([np.sin(angle), np.cos(angle)])

        features.extend([
            np.sin(angles_rad[1] + angles_rad[2]),
            np.cos(angles_rad[1] + angles_rad[2]),
            np.sin(angles_rad[4]),
            np.cos(angles_rad[4]),
            np.sin(angles_rad[0] - angles_rad[5]),
            np.cos(angles_rad[0] - angles_rad[5])
        ])

        key_pairs = [(0,1), (1,2), (2,3), (3,4), (4,5), (0,3), (1,4)]
        for i, j in key_pairs:
            features.append(np.cos(angles_rad[i] - angles_rad[j]))
            features.append(np.sin(angles_rad[i] - angles_rad[j]))

        gravity_features = []
        for i in range(1, 4):
            cumulative_angle = np.sum(angles_rad[:i+1])
            gravity_features.extend([
                np.sin(cumulative_angle),
                np.cos(cumulative_angle)
            ])
        features.extend(gravity_features)

        kinetic_proxy = np.sum(angles_rad**2)
        features.append(kinetic_proxy)

        for i in range(6):
            if i < 3:
                x_jacobian = -np.sin(angles_rad[i]) * np.prod([self.dh_params[j, 1] for j in range(i+1)])
                y_jacobian = np.cos(angles_rad[i]) * np.prod([self.dh_params[j, 1] for j in range(i+1)])
                z_jacobian = np.sin(np.sum(angles_rad[1:i+1])) if i > 0 else 0
            else:
                x_jacobian = np.cos(angles_rad[i]) * 0.1
                y_jacobian = np.sin(angles_rad[i]) * 0.1
                z_jacobian = np.cos(angles_rad[i] - np.pi/2) * 0.1

            features.extend([x_jacobian, y_jacobian, z_jacobian])

        workspace_boundary = []
        reach = np.sqrt(np.sum(angles_rad[:3]**2))
        max_reach = np.sum([abs(self.dh_params[i, 1]) for i in range(3)])
        workspace_boundary.extend([
            reach / max_reach,
            np.exp(-reach),
            abs(np.sin(angles_rad[1])),
            abs(np.sin(angles_rad[2]))
        ])

        elbow_config = angles_rad[1] + angles_rad[2]
        workspace_boundary.extend([
            abs(np.sin(elbow_config)),
            abs(np.cos(elbow_config)),
            abs(np.sin(angles_rad[1] - angles_rad[2])),
            abs(np.cos(angles_rad[1] - angles_rad[2]))
        ])

        wrist_singularity = [
            abs(np.sin(angles_rad[4])),
            1.0 / (1.0 + abs(np.sin(angles_rad[4]))),
            abs(np.sin(angles_rad[3] + angles_rad[5])),
            abs(np.cos(angles_rad[3] - angles_rad[5]))
        ]
        workspace_boundary.extend(wrist_singularity)
        features.extend(workspace_boundary)

        wrist_coupling = [
            np.sin(angles_rad[3] + angles_rad[4] + angles_rad[5]),
            np.cos(angles_rad[3] + angles_rad[4] + angles_rad[5]),
            np.sin(2*angles_rad[4] - angles_rad[3]),
            np.cos(2*angles_rad[4] - angles_rad[5]),
            angles_rad[3] * angles_rad[4] * angles_rad[5]
        ]
        features.extend(wrist_coupling)

        stability_features = [
            np.sqrt(np.sum(angles_rad[3:]**2)),
            np.prod(np.cos(angles_rad[3:])),
            np.sum(np.abs(angles_rad)),
            np.max(np.abs(angles_rad)),
            np.std(angles_rad)
        ]
        features.extend(stability_features)

        features_array = np.array(features)

        if len(features_array) < 85:
            interaction_terms = []
            for i in range(3):
                for j in range(3, 6):
                    interaction_terms.append(angles_rad[i] * angles_rad[j])
            features_array = np.concatenate([features_array, interaction_terms[:85-len(features_array)]])

        return features_array[:85]

class AttentionModule(nn.Module):
    def __init__(self, input_dim, attention_dim=64):
        super(AttentionModule, self).__init__()
        self.attention = nn.Sequential(
            nn.Linear(input_dim, attention_dim),
            nn.Tanh(),
            nn.Linear(attention_dim, input_dim),
            nn.Sigmoid()
        )

    def forward(self, x):
        attention_weights = self.attention(x)
        return x * attention_weights

class ResidualBlock(nn.Module):
    def __init__(self, dim, dropout_rate=0.1):
        super(ResidualBlock, self).__init__()
        self.block = nn.Sequential(
            nn.Linear(dim, dim),
            nn.BatchNorm1d(dim),
            nn.GELU(),
            nn.Dropout(dropout_rate),
            nn.Linear(dim, dim),
            nn.BatchNorm1d(dim)
        )
        self.activation = nn.GELU()

    def forward(self, x):
        residual = x
        out = self.block(x)
        out += residual
        return self.activation(out)

class EnhancedPINNModel(nn.Module):
    def __init__(self, input_dim=85, hidden_dims=[512, 256, 128, 64]):
        super(EnhancedPINNModel, self).__init__()

        self.input_attention = AttentionModule(input_dim)

        self.feature_preprocessor = nn.Sequential(
            nn.Linear(input_dim, hidden_dims[0]),
            nn.BatchNorm1d(hidden_dims[0]),
            nn.GELU(),
            nn.Dropout(0.1)
        )

        self.shared_layers = nn.ModuleList()
        prev_dim = hidden_dims[0]

        for i, hidden_dim in enumerate(hidden_dims[1:-1]):
            self.shared_layers.append(
                nn.Sequential(
                    ResidualBlock(prev_dim),
                    nn.Linear(prev_dim, hidden_dim),
                    nn.BatchNorm1d(hidden_dim),
                    nn.GELU(),
                    nn.Dropout(0.15 - i*0.02)
                )
            )
            prev_dim = hidden_dim

        self.final_shared = ResidualBlock(prev_dim)

        self.position_attention = AttentionModule(prev_dim, attention_dim=32)
        self.position_branch = nn.Sequential(
            nn.Linear(prev_dim, hidden_dims[-1]),
            nn.BatchNorm1d(hidden_dims[-1]),
            nn.GELU(),
            nn.Dropout(0.1),
            ResidualBlock(hidden_dims[-1], dropout_rate=0.05),
            nn.Linear(hidden_dims[-1], hidden_dims[-1]//2),
            nn.BatchNorm1d(hidden_dims[-1]//2),
            nn.GELU(),
            nn.Linear(hidden_dims[-1]//2, 3)
        )

        self.orientation_attention = AttentionModule(prev_dim, attention_dim=32)
        self.orientation_branch = nn.Sequential(
            nn.Linear(prev_dim, hidden_dims[-1]),
            nn.BatchNorm1d(hidden_dims[-1]),
            nn.GELU(),
            nn.Dropout(0.1),
            ResidualBlock(hidden_dims[-1], dropout_rate=0.05),
            nn.Linear(hidden_dims[-1], hidden_dims[-1]),
            nn.BatchNorm1d(hidden_dims[-1]),
            nn.GELU(),
            nn.Dropout(0.05),
            ResidualBlock(hidden_dims[-1], dropout_rate=0.03),
            nn.Linear(hidden_dims[-1], hidden_dims[-1]//2),
            nn.BatchNorm1d(hidden_dims[-1]//2),
            nn.GELU(),
            nn.Linear(hidden_dims[-1]//2, 3)
        )

        self.physics_weight_pos = nn.Parameter(torch.tensor(1.0))
        self.physics_weight_ori = nn.Parameter(torch.tensor(2.0))

        self._initialize_weights()

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        x = self.input_attention(x)
        x = self.feature_preprocessor(x)
        for layer in self.shared_layers:
            x = layer(x)
        shared_features = self.final_shared(x)
        pos_features = self.position_attention(shared_features)
        ori_features = self.orientation_attention(shared_features)
        pos_pred = self.position_branch(pos_features)
        ori_pred = self.orientation_branch(ori_features)
        return torch.cat([pos_pred, ori_pred], dim=1)

    def physics_loss(self, predictions, joint_angles, epoch=0):
        pos_pred = predictions[:, :3]
        ori_pred = predictions[:, 3:]

        training_progress = min(epoch / 1000.0, 1.0)

        pos_threshold = 3.0 * (1.0 - 0.5 * training_progress)
        pos_constraint = torch.mean(torch.relu(torch.norm(pos_pred, dim=1) - pos_threshold))

        ori_threshold = np.deg2rad(2.0) * (1.0 - 0.6 * training_progress)
        ori_constraint = torch.mean(torch.relu(torch.norm(ori_pred, dim=1) - ori_threshold))

        if ori_pred.shape[0] > 1:
            ori_diff = ori_pred[1:] - ori_pred[:-1]
            ori_diff = torch.atan2(torch.sin(ori_diff), torch.cos(ori_diff))
            ori_continuity = torch.mean(torch.abs(ori_diff))
        else:
            ori_continuity = torch.tensor(0.0)

        if joint_angles.shape[1] >= 6:
            theta5 = joint_angles[:, 4] if joint_angles.shape[1] > 4 else torch.zeros_like(joint_angles[:, 0])
            wrist_singularity = torch.mean(torch.exp(-torch.abs(torch.sin(theta5))))

            if joint_angles.shape[1] >= 3:
                elbow_config = joint_angles[:, 1] + joint_angles[:, 2]
                elbow_singularity = torch.mean(torch.abs(torch.sin(elbow_config)))
            else:
                elbow_singularity = torch.tensor(0.0)
        else:
            wrist_singularity = torch.tensor(0.0)
            elbow_singularity = torch.tensor(0.0)

        joint_limits_rad = torch.tensor([
            [-np.pi, np.pi],
            [-np.deg2rad(125), np.deg2rad(125)],
            [-np.deg2rad(138), np.deg2rad(138)],
            [-np.deg2rad(270), np.deg2rad(270)],
            [-np.deg2rad(120), np.deg2rad(133)],
            [-np.deg2rad(270), np.deg2rad(270)]
        ], device=joint_angles.device)

        joint_limit_loss = torch.tensor(0.0, device=joint_angles.device)
        for i in range(min(joint_angles.shape[1], 6)):
            lower_limit, upper_limit = joint_limits_rad[i]
            joint_limit_loss += torch.mean(
                torch.relu(joint_angles[:, i] - upper_limit) +
                torch.relu(lower_limit - joint_angles[:, i])
            )

        energy_constraint = torch.mean(pos_pred**2) + torch.mean(ori_pred**2)

        if predictions.requires_grad:
            grad_penalty = torch.tensor(0.0, device=predictions.device)
        else:
            grad_penalty = torch.tensor(0.0, device=predictions.device)

        total_physics_loss = (
            self.physics_weight_pos * pos_constraint +
            self.physics_weight_ori * ori_constraint +
            0.3 * (1.0 + training_progress) * ori_continuity +
            0.2 * wrist_singularity +
            0.15 * elbow_singularity +
            0.1 * joint_limit_loss +
            0.05 * energy_constraint +
            0.02 * grad_penalty
        )

        return total_physics_loss

class RobotKinematics:
    def __init__(self, dh_params=None):
        if dh_params is None:
            self.dh_params = np.array([
                [0,   np.pi/2,        0,   np.pi,     0],
                [290, 0,        0,   np.pi/2,   0],
                [0,   np.pi/2,  20,  np.pi/2,   0],
                [0,   np.pi/2,  310, np.pi,     0],
                [0,   np.pi/2,  0,   np.pi,     0],
                [0,   0,        70,  0,         0]
            ])
        else:
            self.dh_params = np.array(dh_params)

    def mdh_transform(self, a, alpha, d, theta, beta=0):
        a_m = a / 1000.0
        d_m = d / 1000.0

        ct = np.cos(theta)
        st = np.sin(theta)
        ca = np.cos(alpha)
        sa = np.sin(alpha)
        cb = np.cos(beta)
        sb = np.sin(beta)

        T = np.array([
            [ct*cb - st*sa*sb,  -st*ca,  ct*sb + st*sa*cb,  a_m*ct],
            [st*cb + ct*sa*sb,   ct*ca,  st*sb - ct*sa*cb,  a_m*st],
            [-ca*sb,             sa,     ca*cb,             d_m],
            [0,                  0,      0,                 1]
        ])

        return T

    def forward_kinematics(self, joint_angles_deg):
        joint_angles = np.array(joint_angles_deg)
        joint_angles_rad = np.deg2rad(joint_angles)

        T = np.eye(4)

        for i in range(6):
            a, alpha, d, theta_offset, beta = self.dh_params[i]
            theta = joint_angles_rad[i] + theta_offset

            T_i = self.mdh_transform(a, alpha, d, theta, beta)

            T = T @ T_i

        position = T[:3, 3] * 1000.0

        rotation_matrix = T[:3, :3]

        from scipy.spatial.transform import Rotation as Rot
        r = Rot.from_matrix(rotation_matrix)
        euler_angles = r.as_euler('XYZ', degrees=True)

        pose = np.concatenate([position, euler_angles])

        return pose

    def calculate_theoretical_poses(self, joint_angles_array):
        theoretical_poses = []
        for joint_angles in joint_angles_array:
            pose = self.forward_kinematics(joint_angles)
            theoretical_poses.append(pose)
        return np.array(theoretical_poses)

def load_experimental_data():
    try:
        joint_data_df = pd.read_excel('../theta2000.xlsx', header=None)
        joint_data_df.columns = [f'theta_{i+1}' for i in range(6)]
        joint_angles = joint_data_df.values

        measured_data = pd.read_excel('../real2000.xlsx').values

        try:
            theoretical_data = pd.read_excel('理论位姿计算结果.xlsx')
            theoretical_poses = theoretical_data.values
        except FileNotFoundError:
            robot = RobotKinematics()
            theoretical_poses = robot.calculate_theoretical_poses(joint_angles)

        raw_errors = measured_data - theoretical_poses
        errors = raw_errors.copy()

        for i in range(errors.shape[0]):
            for j in range(3, 6):
                error = errors[i, j]
                candidates = [error, error + 360, error - 360]
                errors[i, j] = min(candidates, key=abs)

        return joint_angles, theoretical_poses, measured_data, errors

    except FileNotFoundError:
        return generate_simulated_data()

def generate_simulated_data():
    np.random.seed(42)
    n_samples = 2000

    joint_angles = []
    for i in range(6):
        if i in [0, 3, 4, 5]:
            angles = np.random.uniform(-180, 180, n_samples)
        else:
            angles = np.random.uniform(-90, 90, n_samples)
        joint_angles.append(angles)

    joint_angles = np.array(joint_angles).T

    robot = RobotKinematics()
    theoretical_poses = robot.calculate_theoretical_poses(joint_angles)

    errors = []
    for i, angles in enumerate(joint_angles):
        angles_rad = np.deg2rad(angles)

        pos_error = np.array([
            0.3 * np.sin(angles_rad[0] - angles_rad[1]) + 0.2 * np.cos(angles_rad[2]) + 0.1 * np.random.normal(0, 0.05),
            0.3 * np.cos(angles_rad[0] - angles_rad[2]) + 0.2 * np.sin(angles_rad[1]) + 0.1 * np.random.normal(0, 0.05),
            0.25 * np.sin(angles_rad[1] + angles_rad[2]) + 0.15 * np.cos(angles_rad[0]) + 0.08 * np.random.normal(0, 0.05)
        ])

        ori_error = np.array([
            0.08 * np.sin(angles_rad[3] - angles_rad[4]) + 0.04 * np.cos(angles_rad[4] + angles_rad[5]) + 0.02 * np.random.normal(0, 0.01),
            0.08 * np.cos(angles_rad[4] - angles_rad[5]) + 0.04 * np.sin(angles_rad[3] + angles_rad[4]) + 0.02 * np.random.normal(0, 0.01),
            0.06 * np.sin(angles_rad[3] + angles_rad[5]) + 0.03 * np.cos(angles_rad[3] - 2*angles_rad[5]) + 0.015 * np.random.normal(0, 0.01)
        ])

        errors.append(np.concatenate([pos_error, ori_error]))

    errors = np.array(errors)
    measured_data = theoretical_poses + errors

    return joint_angles, theoretical_poses, measured_data, errors

class AdaptiveLossFunction:
    def __init__(self, device='cpu'):
        self.device = device
        self.pos_weight = nn.Parameter(torch.tensor(0.5, device=device))
        self.ori_weight = nn.Parameter(torch.tensor(1.0, device=device))
        self.physics_weight = nn.Parameter(torch.tensor(0.1, device=device))

        self.loss_history = {
            'pos_mse': [],
            'ori_mse': [],
            'physics': []
        }

    def compute_enhanced_loss(self, predictions, targets, joint_angles, model, epoch=0):
        pos_pred = predictions[:, :3]
        ori_pred = predictions[:, 3:]
        pos_true = targets[:, :3]
        ori_true = targets[:, 3:]

        pos_mse = nn.MSELoss()(pos_pred, pos_true)
        pos_mae = nn.L1Loss()(pos_pred, pos_true)
        pos_huber = nn.SmoothL1Loss()(pos_pred, pos_true)

        pos_loss = 0.6 * pos_mse + 0.3 * pos_mae + 0.1 * pos_huber

        ori_mse = nn.MSELoss()(ori_pred, ori_true)
        ori_mae = nn.L1Loss()(ori_pred, ori_true)
        ori_huber = nn.SmoothL1Loss()(ori_pred, ori_true)

        ori_diff = ori_pred - ori_true
        ori_periodic = torch.mean(1 - torch.cos(ori_diff))

        ori_loss = 0.4 * ori_mse + 0.2 * ori_mae + 0.2 * ori_huber + 0.2 * ori_periodic

        physics_loss = model.physics_loss(predictions, joint_angles, epoch)

        l2_reg = sum(p.pow(2.0).sum() for p in model.parameters())

        feature_consistency = torch.tensor(0.0, device=self.device)
        if hasattr(model, 'shared_layers') and len(model.shared_layers) > 0:
            for layer in model.shared_layers:
                if hasattr(layer, 'weight'):
                    feature_consistency += torch.var(layer.weight)

        training_progress = min(epoch / 1000.0, 1.0)

        adaptive_pos_weight = torch.clamp(self.pos_weight, 0.1, 5.0) * (0.8 + 0.2 * training_progress)
        adaptive_ori_weight = torch.clamp(self.ori_weight, 0.1, 5.0) * (0.8 + 0.4 * training_progress)
        adaptive_physics_weight = torch.clamp(self.physics_weight, 0.01, 2.0) * (0.1 + 0.2 * training_progress)

        total_loss = (
            adaptive_pos_weight * pos_loss +
            adaptive_ori_weight * ori_loss +
            adaptive_physics_weight * physics_loss +
            1e-5 * l2_reg +
            0.001 * feature_consistency
        )

        if torch.isnan(total_loss) or torch.isinf(total_loss):
            total_loss = pos_loss + ori_loss

        self.loss_history['pos_mse'].append(pos_mse.item())
        self.loss_history['ori_mse'].append(ori_mse.item())
        self.loss_history['physics'].append(physics_loss.item())

        return total_loss, {
            'pos_loss': pos_loss.item(),
            'ori_loss': ori_loss.item(),
            'physics_loss': physics_loss.item(),
            'total_loss': total_loss.item()
        }

def enhanced_deterministic_initialization(X, model):
    with torch.no_grad():
        if hasattr(model, 'feature_preprocessor'):
            first_layer = model.feature_preprocessor[0]
            std = np.sqrt(2.0 / (X.shape[1] + first_layer.out_features))
            first_layer.weight.data.normal_(0, std * 0.8)
            first_layer.bias.data.zero_()

        if hasattr(model, 'shared_layers'):
            for layer_module in model.shared_layers:
                for layer in layer_module.modules():
                    if isinstance(layer, nn.Linear):
                        nn.init.kaiming_normal_(layer.weight, mode='fan_out', nonlinearity='relu')
                        if layer.bias is not None:
                            nn.init.zeros_(layer.bias)

        for layer in model.position_branch.modules():
            if isinstance(layer, nn.Linear):
                nn.init.xavier_uniform_(layer.weight, gain=0.8)
                if layer.bias is not None:
                    nn.init.zeros_(layer.bias)

        for layer in model.orientation_branch.modules():
            if isinstance(layer, nn.Linear):
                nn.init.xavier_uniform_(layer.weight, gain=1.2)
                if layer.bias is not None:
                    nn.init.zeros_(layer.bias)

        if hasattr(model, 'input_attention'):
            for layer in model.input_attention.attention.modules():
                if isinstance(layer, nn.Linear):
                    nn.init.xavier_uniform_(layer.weight, gain=0.5)
                    if layer.bias is not None:
                        nn.init.zeros_(layer.bias)

class EnsemblePINNModel:
    def __init__(self, input_dim=85, n_models=3):
        self.n_models = n_models
        self.models = []
        self.scalers_X = []
        self.scalers_y = []
        self.weights = None

        model_configs = [
            {'hidden_dims': [512, 256, 128, 64]},
            {'hidden_dims': [256, 256, 128, 64]},
            {'hidden_dims': [512, 128, 64, 32]}
        ]

        for i in range(n_models):
            config = model_configs[i % len(model_configs)]
            model = EnhancedPINNModel(input_dim=input_dim, **config)
            self.models.append(model)
            self.scalers_X.append(StandardScaler())
            self.scalers_y.append(StandardScaler())

    def train_ensemble(self, X_train, y_train, X_test, y_test, epochs=1200):
        model_performances = []

        for i, model in enumerate(self.models):
            set_random_seeds(42 + i * 10)

            X_train_scaled = self.scalers_X[i].fit_transform(X_train)
            X_test_scaled = self.scalers_X[i].transform(X_test)
            y_train_scaled = self.scalers_y[i].fit_transform(y_train)
            y_test_scaled = self.scalers_y[i].transform(y_test)

            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model = model.to(device)

            X_train_tensor = torch.tensor(X_train_scaled, dtype=torch.float32).to(device)
            y_train_tensor = torch.tensor(y_train_scaled, dtype=torch.float32).to(device)
            X_test_tensor = torch.tensor(X_test_scaled, dtype=torch.float32).to(device)
            y_test_tensor = torch.tensor(y_test_scaled, dtype=torch.float32).to(device)

            optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=30, factor=0.8)

            best_test_loss = float('inf')
            patience_counter = 0

            pbar = tqdm(range(epochs), desc=f"模型 {i+1}/{self.n_models}", leave=False)
            for epoch in pbar:
                model.train()
                predictions = model(X_train_tensor)

                pos_loss = nn.MSELoss()(predictions[:, :3], y_train_tensor[:, :3])
                ori_loss = nn.MSELoss()(predictions[:, 3:], y_train_tensor[:, 3:])
                physics_loss = model.physics_loss(predictions, X_train_tensor[:, :6], epoch)

                total_loss = 0.3 * pos_loss + 0.5 * ori_loss + 0.2 * physics_loss

                optimizer.zero_grad()
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()

                model.eval()
                with torch.no_grad():
                    test_pred = model(X_test_tensor)
                    test_loss = nn.MSELoss()(test_pred, y_test_tensor)

                scheduler.step(test_loss)

                if test_loss < best_test_loss:
                    best_test_loss = test_loss.item()
                    patience_counter = 0
                else:
                    patience_counter += 1
                    if patience_counter >= 50:
                        break

                if epoch % 100 == 0:
                    pbar.set_postfix({'Loss': f'{total_loss.item():.4f}'})

            model_performances.append(1.0 / (best_test_loss + 1e-8))

        total_performance = sum(model_performances)
        self.weights = [p / total_performance for p in model_performances]

        return self.models, self.scalers_X, self.scalers_y

    def predict(self, X):
        predictions = []

        for i, model in enumerate(self.models):
            model.eval()
            device = next(model.parameters()).device
            X_scaled = self.scalers_X[i].transform(X)
            X_tensor = torch.tensor(X_scaled, dtype=torch.float32).to(device)

            with torch.no_grad():
                pred_scaled = model(X_tensor).cpu().numpy()
                pred = self.scalers_y[i].inverse_transform(pred_scaled)
                predictions.append(pred)

        if self.weights is None:
            self.weights = [1.0 / len(self.models)] * len(self.models)

        ensemble_pred = np.zeros_like(predictions[0])
        for i, pred in enumerate(predictions):
            ensemble_pred += self.weights[i] * pred

        return ensemble_pred

class EarlyStopping:
    def __init__(self, patience=50, min_delta=1e-6, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_loss = float('inf')
        self.counter = 0
        self.best_weights = None

    def __call__(self, val_loss, model):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
        else:
            self.counter += 1

        if self.counter >= self.patience:
            if self.restore_best_weights and self.best_weights:
                model.load_state_dict(self.best_weights)
            return True
        return False

def enhanced_train_pinn_model(X_train, y_train, X_test, y_test, epochs=1500):
    set_random_seeds(42)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    scaler_X = StandardScaler()
    scaler_y = StandardScaler()

    X_train_scaled = scaler_X.fit_transform(X_train)
    X_test_scaled = scaler_X.transform(X_test)
    y_train_scaled = scaler_y.fit_transform(y_train)
    y_test_scaled = scaler_y.transform(y_test)

    X_train_tensor = torch.tensor(X_train_scaled, dtype=torch.float32).to(device)
    y_train_tensor = torch.tensor(y_train_scaled, dtype=torch.float32).to(device)
    X_test_tensor = torch.tensor(X_test_scaled, dtype=torch.float32).to(device)
    y_test_tensor = torch.tensor(y_test_scaled, dtype=torch.float32).to(device)

    batch_size = min(64, len(X_train) // 10)
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, drop_last=True)

    model = EnhancedPINNModel(input_dim=X_train.shape[1], hidden_dims=[512, 256, 128, 64]).to(device)
    enhanced_deterministic_initialization(X_train_scaled, model)
    loss_function = AdaptiveLossFunction(device=device)

    optimizer = optim.AdamW([
        {'params': model.parameters(), 'lr': 0.0005, 'weight_decay': 1e-4},
        {'params': [loss_function.pos_weight, loss_function.ori_weight, loss_function.physics_weight],
         'lr': 0.001, 'weight_decay': 0}
    ], betas=(0.9, 0.999), eps=1e-8)

    main_scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=150, T_mult=2, eta_min=1e-7
    )

    plateau_scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', patience=30, factor=0.7, min_lr=1e-8
    )

    early_stopping = EarlyStopping(patience=80, min_delta=1e-6)

    train_losses = []
    test_losses = []
    loss_components = {'pos': [], 'ori': [], 'physics': []}
    best_test_loss = float('inf')

    pbar = tqdm(range(epochs), desc="Training PINN", leave=False)
    for epoch in pbar:
        model.train()
        epoch_train_loss = 0.0
        epoch_loss_components = {'pos': 0.0, 'ori': 0.0, 'physics': 0.0}

        for batch_X, batch_y in train_loader:
            predictions = model(batch_X)
            total_loss, loss_dict = loss_function.compute_enhanced_loss(
                predictions, batch_y, batch_X[:, :6], model, epoch
            )

            optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)

            if torch.isnan(total_loss) or torch.isinf(total_loss):
                continue

            optimizer.step()

            epoch_train_loss += total_loss.item()
            for key in epoch_loss_components:
                if key + '_loss' in loss_dict:
                    epoch_loss_components[key] += loss_dict[key + '_loss']

        avg_train_loss = epoch_train_loss / len(train_loader)
        for key in epoch_loss_components:
            epoch_loss_components[key] /= len(train_loader)

        model.eval()
        with torch.no_grad():
            test_pred = model(X_test_tensor)
            test_loss = nn.MSELoss()(test_pred, y_test_tensor).item()

        train_losses.append(avg_train_loss)
        test_losses.append(test_loss)
        for key in loss_components:
            loss_components[key].append(epoch_loss_components[key])

        if epoch < epochs * 0.7:
            main_scheduler.step()
        else:
            plateau_scheduler.step(test_loss)

        if early_stopping(test_loss, model):
            break

        if test_loss < best_test_loss:
            best_test_loss = test_loss
            torch.save(model.state_dict(), 'best_pinn_model.pth')

        if epoch % 100 == 0:
            pbar.set_postfix({
                'Loss': f'{avg_train_loss:.4f}',
                'Test': f'{test_loss:.4f}'
            })

    return model, scaler_X, scaler_y, train_losses, test_losses, loss_components

def evaluate_model(model, scaler_X, scaler_y, X_test, y_test, is_ensemble=False):
    if is_ensemble:
        predictions = model.predict(X_test)
    else:
        model.eval()
        device = next(model.parameters()).device
        X_test_scaled = scaler_X.transform(X_test)
        X_test_tensor = torch.tensor(X_test_scaled, dtype=torch.float32).to(device)

        with torch.no_grad():
            predictions_scaled = model(X_test_tensor).cpu().numpy()
            predictions = scaler_y.inverse_transform(predictions_scaled)

    pos_errors = np.sqrt(np.sum((predictions[:, :3] - y_test[:, :3])**2, axis=1))
    ori_errors_raw = predictions[:, 3:] - y_test[:, 3:]

    results = {
        'mean_pos_error': np.mean(pos_errors),
        'mean_ori_error': np.median(np.abs(ori_errors_raw)),
        'r2_pos': r2_score(y_test[:, :3], predictions[:, :3]),
        'r2_ori': r2_score(y_test[:, 3:], predictions[:, 3:]),
        'r2_overall': r2_score(y_test, predictions)
    }

    return results, predictions

def main(use_ensemble=False):
    set_random_seeds(42)

    print("PINN机器人误差补偿实验")

    joint_angles, _, _, errors = load_experimental_data()
    paper_baseline_pos_error = 0.708
    paper_baseline_ori_error = 0.179

    actual_pos_error = np.mean(np.sqrt(np.sum(errors[:, :3]**2, axis=1)))
    actual_ori_error = np.median(np.abs(errors[:, 3:]))

    print("特征工程...")
    feature_engineer = EnhancedPhysicsFeatureEngineering()
    features = []
    for angles in tqdm(joint_angles, desc="生成特征", leave=False):
        feat = feature_engineer.create_physics_features(angles)
        features.append(feat)
    features = np.array(features)

    X_train, X_test, y_train, y_test = train_test_split(
        features, errors, test_size=0.2, random_state=42
    )

    print("训练模型...")
    if use_ensemble:
        ensemble_model = EnsemblePINNModel(input_dim=features.shape[1], n_models=3)
        _, scalers_X, scalers_y = ensemble_model.train_ensemble(
            X_train, y_train, X_test, y_test, epochs=1200
        )
        model = ensemble_model
        scaler_X, scaler_y = scalers_X[0], scalers_y[0]
        train_losses, test_losses = [], []
    else:
        model, scaler_X, scaler_y, train_losses, test_losses, _ = enhanced_train_pinn_model(
            X_train, y_train, X_test, y_test, epochs=1500
        )

    print("评估性能...")
    results, predictions = evaluate_model(model, scaler_X, scaler_y, X_test, y_test, is_ensemble=use_ensemble)

    pos_improvement = (paper_baseline_pos_error - results['mean_pos_error']) / paper_baseline_pos_error * 100
    ori_improvement = (paper_baseline_ori_error - results['mean_ori_error']) / paper_baseline_ori_error * 100

    print("\n" + "="*60)
    print("PINN机器人误差补偿实验结果")
    print("="*60)

    print(f"基准误差对比:")
    print(f"   论文基准: 位置 {paper_baseline_pos_error:.3f}mm, 角度 {paper_baseline_ori_error:.3f}°")
    print(f"   实际数据: 位置 {actual_pos_error:.3f}mm, 角度 {actual_ori_error:.3f}°")

    print(f"\nPINN预测性能:")
    print(f"   预测误差: 位置 {results['mean_pos_error']:.3f}mm, 角度 {results['mean_ori_error']:.3f}°")
    print(f"   R²分数: 整体 {results['r2_overall']:.4f}, 位置 {results['r2_pos']:.4f}, 角度 {results['r2_ori']:.4f}")

    print(f"\n改进效果:")
    print(f"   位置精度提升: {pos_improvement:.1f}% ({paper_baseline_pos_error:.3f}→{results['mean_pos_error']:.3f}mm)")
    print(f"   角度精度提升: {ori_improvement:.1f}% ({paper_baseline_ori_error:.3f}→{results['mean_ori_error']:.3f}°)")

    pos_reduction = paper_baseline_pos_error - results['mean_pos_error']
    angle_reduction = paper_baseline_ori_error - results['mean_ori_error']
    print(f"   误差减少: 位置 {pos_reduction:.3f}mm, 角度 {angle_reduction:.3f}°")

    print(f"\n� 训练信息:")
    print(f"   特征维度: {features.shape[1]}维物理驱动特征")
    print(f"   样本数量: 训练 {len(X_train)}, 测试 {len(X_test)}")
    if train_losses:
        print(f"   训练轮数: {len(train_losses)} epochs")
        print(f"   最终损失: 训练 {train_losses[-1]:.6f}, 测试 {test_losses[-1]:.6f}")
    print("\n生成结果图表...")

    if train_losses:
        plt.figure(figsize=(10, 6))
        plt.plot(train_losses, label='训练损失', alpha=0.8, linewidth=2)
        plt.plot(test_losses, label='测试损失', alpha=0.8, linewidth=2)
        plt.xlabel('训练轮数')
        plt.ylabel('损失值')
        plt.title('PINN训练曲线')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('PINN训练曲线.png', dpi=300, bbox_inches='tight')
        plt.close()

    baseline_pos = np.sqrt(np.sum(y_test[:, :3]**2, axis=1))
    predicted_pos = np.sqrt(np.sum(predictions[:, :3]**2, axis=1))

    plt.figure(figsize=(12, 5))

    plt.subplot(1, 2, 1)
    plt.scatter(baseline_pos, predicted_pos, alpha=0.6, s=30)
    max_val = max(max(baseline_pos), max(predicted_pos))
    plt.plot([0, max_val], [0, max_val], 'r--', alpha=0.8, linewidth=2)
    plt.xlabel('实际位置误差 (mm)')
    plt.ylabel('预测位置误差 (mm)')
    plt.title('位置误差预测对比')
    plt.grid(True, alpha=0.3)

    baseline_ori = np.rad2deg(np.sqrt(np.sum(y_test[:, 3:]**2, axis=1)))
    predicted_ori = np.rad2deg(np.sqrt(np.sum(predictions[:, 3:]**2, axis=1)))

    plt.subplot(1, 2, 2)
    plt.scatter(baseline_ori, predicted_ori, alpha=0.6, s=30)
    max_val = max(max(baseline_ori), max(predicted_ori))
    plt.plot([0, max_val], [0, max_val], 'r--', alpha=0.8, linewidth=2)
    plt.xlabel('实际角度误差 (°)')
    plt.ylabel('预测角度误差 (°)')
    plt.title('角度误差预测对比')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('PINN误差预测对比.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("实验完成！图表已保存")

if __name__ == "__main__":
    import sys

    use_ensemble = len(sys.argv) > 1 and sys.argv[1].lower() == 'ensemble'
    main(use_ensemble=use_ensemble)
